package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.ConfiguracaoSorteioDTO;
import com.pacto.adm.core.dto.enveloperesposta.configuracao.EnvelopeRespostaConfiguracaoSorteioDTO;
import com.pacto.adm.core.services.interfaces.ConfiguracaoSorteioService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/configuracao-sorteio")
@Tag(name = "Sorteio")
public class ConfiguracaoSorteioController {

    @Autowired
    ConfiguracaoSorteioService configuracaoSorteioService;


    @Operation(
            summary = "Criar ou atualizar as configurações do sorteio",
            description = "Cria ou atualiza as configurações do sorteio.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar as configurações do sorteio.<br/>" +
                            "Se for criar uma nova configuração, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar uma configuração, envie a requisição com o atribbuto código contendo o valor da configuração que será atualizada.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConfiguracaoSorteioDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaConfiguracaoSorteioDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoSorteioDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200",
                                    value = "{\n" +
                                            "  \"content\": { " +
                                                "  \"codigo\": 2,\n" +
                                                "  \"planos\": [\n" +
                                                "    {\n" +
                                                "      \"codigo\": 1,\n" +
                                                "      \"descricao\": \"Plano Básico Mensal\",\n" +
                                                "      \"quantidadeCompartilhamentos\": 5,\n" +
                                                "      \"restringeVendaPorCategoria\": true,\n" +
                                                "      \"categorias\": [1, 2, 3],\n" +
                                                "      \"bloquearRecompra\": false,\n" +
                                                "      \"permitirTransferenciaDeCredito\": false\n" +
                                                "    }\n" +
                                                "  ],\n" +
                                                "  \"situacoesCliente\": [\"AT\", \"VI\"],\n" +
                                                "  \"empresa\": 1\n" +
                                            "   }" +
                                            "}"
                                    )
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody ConfiguracaoSorteioDTO configuracaoSorteioDTO) {
        try {
            return ResponseEntityFactory.ok(configuracaoSorteioService.saveOrUpdate(configuracaoSorteioDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar configuração de sorteio por empresa",
            description = "Consulta a configuração de sorteio pelo id da empresa.",
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa que se deseja consultar", required = true, example = "1", in = ParameterIn.PATH)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConfiguracaoSorteioDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200",
                                            value = "{\n" +
                                                    "  \"content\": { " +
                                                    "  \"codigo\": 2,\n" +
                                                    "  \"planos\": [\n" +
                                                    "    {\n" +
                                                    "      \"codigo\": 1,\n" +
                                                    "      \"descricao\": \"Plano Básico Mensal\",\n" +
                                                    "      \"quantidadeCompartilhamentos\": 5,\n" +
                                                    "      \"restringeVendaPorCategoria\": true,\n" +
                                                    "      \"categorias\": [1, 2, 3],\n" +
                                                    "      \"bloquearRecompra\": false,\n" +
                                                    "      \"permitirTransferenciaDeCredito\": false\n" +
                                                    "    }\n" +
                                                    "  ],\n" +
                                                    "  \"situacoesCliente\": [\"AT\", \"VI\"],\n" +
                                                    "  \"empresa\": 1\n" +
                                                    "   }" +
                                                    "}"
                                    )
                            )
                    )
            }
    )
    @GetMapping("/{empresaId}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(configuracaoSorteioService.findByEmpresaId(empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}

