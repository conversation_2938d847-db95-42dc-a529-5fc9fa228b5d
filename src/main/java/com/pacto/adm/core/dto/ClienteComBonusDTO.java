package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Cliente com Bônus", description = "Informações de um cliente que recebeu bônus em seu contrato")
public class ClienteComBonusDTO {

    @Schema(description = "Informações detalhadas do cliente que recebeu o bônus")
    private ClienteDTO cliente;

    @Schema(description = "Informações detalhadas da operação de bônus realizada no contrato")
    private ContratoOperacaoDTO contratoOperacao;

    @Schema(description = "Informações detalhadas do contrato onde o bônus foi aplicado")
    private ContratoDTO contrato;

    public ClienteComBonusDTO(ClienteDTO cliente, ContratoDTO contrato, ContratoOperacaoDTO contratoOperacao) {
        this.cliente = cliente;
        this.contrato = contrato;
        this.contratoOperacao = contratoOperacao;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public ContratoOperacaoDTO getContratoOperacao() {
        return contratoOperacao;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }
}
