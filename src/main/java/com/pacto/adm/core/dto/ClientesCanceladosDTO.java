package com.pacto.adm.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

@Schema(name = "Cliente Cancelado", description = "Informações de um cliente que teve seu contrato cancelado")
public class ClientesCanceladosDTO {

    @Schema(description = "Código único identificador do cliente", example = "12345")
    private Integer codigoCliente;

    @Schema(description = "Matrícula do cliente no formato texto", example = "001234")
    private String matriculaCliente;

    @Schema(description = "Nome completo do cliente", example = "<PERSON>")
    private String nomeCliente;

    @Schema(description = "Código único identificador do contrato cancelado", example = "67890")
    private Integer codigoContrato;

    @Schema(description = "Descrição da justificativa utilizada para o cancelamento", example = "Transferência para outro aluno")
    private String descricaoJustificativa;

    @Schema(description = "Nome do usuário responsável pela operação de cancelamento", example = "Maria Oliveira")
    private String responsavelOperacao;

    @Schema(description = "Data e hora em que a operação de cancelamento foi realizada", example = "2024-01-15T10:30:00.000Z")
    private Date dataOperacao;

    public ClientesCanceladosDTO(
            Integer codigoCliente, String matriculaCliente, String nomeCliente, Integer codigoContrato,
            String descricaoJustificativa, String responsavelOperacao, Date dataOperacao
    ) {
        this.codigoCliente = codigoCliente;
        this.matriculaCliente = matriculaCliente;
        this.nomeCliente = nomeCliente;
        this.codigoContrato = codigoContrato;
        this.descricaoJustificativa = descricaoJustificativa;
        this.responsavelOperacao = responsavelOperacao;
        this.dataOperacao = dataOperacao;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getDescricaoJustificativa() {
        return descricaoJustificativa;
    }

    public void setDescricaoJustificativa(String descricaoJustificativa) {
        this.descricaoJustificativa = descricaoJustificativa;
    }

    public String getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(String responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }
}
