package com.pacto.adm.core.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pacto.adm.core.enumerador.TipoOperacaoContratoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import com.pacto.config.utils.Calendario;

import java.util.Date;

@JsonInclude
@Schema(name = "Operações de Contrato", description = "Informações de contrato operação")
public class ContratoOperacaoDTO {


    @Schema(description = "Código Único Identificador da operação do contrato", example = "65")
    private Integer codigo;

    @Schema(description = "Quantidade de dias para trasnferência", example = "10")
    private Integer clienteTransfereDias;

    @Schema(description = "Quantidade de dias para recebimento", example = "15")
    private Integer clienteRecebeDias;

    @Schema(description = "Descrição do cálculo", example = "Adicionar aulas de natação ao contrato")
    private String descricaoCalculo;

    @Schema(description = "Observação da operação", example = "Cliente optou por adicionar aulas de natação ao seu contrato")
    private String observacao;

    @Schema(description = "Data da operação", example = "2025-05-07T00:00:00")
    private Date dataOperacao;

    @Schema(description = "Data de início da efetivação da operação", example = "2025-05-08T00:00:00")
    private Date dataInicioEfetivacaoOperacao;

    @Schema(description = "Data de fim da efetivação da operação", example = "2025-05-10T00:00:00")
    private Date dataFimEfetivacaoOperacao;

    @Schema(description = "Indica se a operação foi paga", example = "true")
    private Boolean operacaoPaga;

    @Schema(description = "Tipo da operação realizada no contrato. \n\n" +
            "**Valores disponíveis**\n" +
            "- TS (TRANSFERENCIA_SAIDA)\n" +
            "- AD (ALTERACAO_DURACAO)\n" +
            "- BA (BONUS_ACRESCIMO)\n" +
            "- BR (BONUS_REDUCAO)\n" +
            "- CR (CARENCIA)\n" +
            "- TE (TRANSFERENCIA_ENTRADA)\n" +
            "- CA (CANCELAMENTO)\n" +
            "- AH (ALTERACAO_HORARIO)\n" +
            "- TR (TRANCAMENTO)\n" +
            "- TV (TRANCAMENTO_VENCIDO)\n" +
            "- RT (RETORNO_TRANCAMENTO)\n" +
            "- IM (INCLUIR_MODALIDADE)\n" +
            "- AM (ALTERAR_MODALIDADE)\n" +
            "- EM (EXCLUIR_MODALIDADE)\n" +
            "- AC (ALTERACAO_CONTRATO)\n" +
            "- AT (ATESTADO)\n" +
            "- RA (RETORNO_ATESTADO)\n" +
            "- LV (LIBERAR_VAGA)\n" +
            "- BC (BONUS_COLETIVO)\n", example = "BA")
    private String tipoOperacao;

    @Schema(description = "Valor da operação", example = "20.00")
    private Double valor;

    @Schema(description = "Número de dias da operação", example = "1")
    private Integer nrDiasOperacao;

    @Schema(description = "Informações da operação", example = "CPF, RG, Código do Contrato e Exame Médico do Cliente para adicionar as aulas de natação")
    private String informacoes;

    @Schema(description = "Chave do arquivo da operação", example = "4568723425")
    private String chaveArquivo;

    @Schema(description = "Nome do arquivo da operação", example = "contrato-natacao")
    private String nomeArquivo;

    @Schema(description = "Formato do arquivo da operação", example = "pdf")
    private String formatoArquivo;

    @Schema(description = "Origem do Sistema" +
            "Os possíveis valores para o atributo são: " +
            "1 (ZillyonWeb), " +
            "2 (Agenda Web), " +
            "3 (Pacto Treino), " +
            "4 (App Treino), " +
            "5 (App Professor), " +
            "6 (Autoatendimento), " +
            "7 (Site Vendas), " +
            "8 (Buzz Lead), " +
            "9 (Vendas 2.0), " +
            "10 (App do consultor), " +
            "11 (Booking Gympass), " +
            "12 (Fila de espera), " +
            "13 (Importação API), " +
            "14 (Hubspot Lead), " +
            "15 (CRM Meta Diaria), " +
            "16 (Pacto Flow), " +
            "17 (Nova Tela de Negociação).", example = "1")
    private Integer origemSistema;

    @Schema(description = "Informações para desfazer a operação", example = "Apenas CPF, RG e assinatura do cliente")
    private String informacoesDesfazer;

    @Schema(description = "Contrato vinculado a operação")
    private ContratoDTO contrato;

    @Schema(description = "Justificativa da operação")
    private JustificativaOperacaoDTO tipoJustificativa;

    @Schema(description = "Usuário responsável por fazer a operação")
    private UsuarioDTO responsavel;

    @Schema(description = "Usuário responsável pela liberação da operação")
    private UsuarioDTO responsavelLiberacao;

    @Schema(description = "Dias da operação", example = "1L")
    private Long diasOperacao;

    @Schema(description = "Url do arquivo atestado (Caso a operação seja de atestado médico)", example = "www.exemplo.pactosolucoes/arquivos/atestado.pdf")
    private String urlArquivoAtestado;

    @Schema(description = "Indica se permite estornar a operação", example = "true")
    private boolean permiteEstornar = false;

    public ContratoOperacaoDTO() {
    }

    public ContratoOperacaoDTO(Date dataOperacao, Date dataInicioEfetivacaoOperacao, Date dataFimEfetivacaoOperacao, JustificativaOperacaoDTO tipoJustificativa) {
        this.dataOperacao = dataOperacao;
        this.dataInicioEfetivacaoOperacao = dataInicioEfetivacaoOperacao;
        this.dataFimEfetivacaoOperacao = dataFimEfetivacaoOperacao;
        this.tipoJustificativa = tipoJustificativa;
    }

    public ContratoOperacaoDTO(Integer codigo, String tipoOperacao, Date dataLancamento, Date dataInicio, Date dataFim, UsuarioDTO responsavel) {
        this.codigo = codigo;
        this.tipoOperacao = tipoOperacao;
        this.dataOperacao = dataLancamento;
        this.dataInicioEfetivacaoOperacao = dataInicio;
        this.dataFimEfetivacaoOperacao = dataFim;
        this.responsavel = responsavel;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getClienteTransfereDias() {
        return clienteTransfereDias;
    }

    public void setClienteTransfereDias(Integer clienteTransfereDias) {
        this.clienteTransfereDias = clienteTransfereDias;
    }

    public Integer getClienteRecebeDias() {
        return clienteRecebeDias;
    }

    public void setClienteRecebeDias(Integer clienteRecebeDias) {
        this.clienteRecebeDias = clienteRecebeDias;
    }

    public String getDescricaoCalculo() {
        return descricaoCalculo;
    }

    public void setDescricaoCalculo(String descricaoCalculo) {
        this.descricaoCalculo = descricaoCalculo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public Date getDataInicioEfetivacaoOperacao() {
        return dataInicioEfetivacaoOperacao;
    }

    public void setDataInicioEfetivacaoOperacao(Date dataInicioEfetivacaoOperacao) {
        this.dataInicioEfetivacaoOperacao = dataInicioEfetivacaoOperacao;
    }

    public Date getDataFimEfetivacaoOperacao() {
        return dataFimEfetivacaoOperacao;
    }

    public void setDataFimEfetivacaoOperacao(Date dataFimEfetivacaoOperacao) {
        this.dataFimEfetivacaoOperacao = dataFimEfetivacaoOperacao;
    }

    public Boolean getOperacaoPaga() {
        return operacaoPaga;
    }

    public void setOperacaoPaga(Boolean operacaoPaga) {
        this.operacaoPaga = operacaoPaga;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getNrDiasOperacao() {
        return nrDiasOperacao;
    }

    public void setNrDiasOperacao(Integer nrDiasOperacao) {
        this.nrDiasOperacao = nrDiasOperacao;
    }

    public String getInformacoes() {
        return informacoes;
    }

    public void setInformacoes(String informacoes) {
        this.informacoes = informacoes;
    }

    public String getChaveArquivo() {
        return chaveArquivo;
    }

    public void setChaveArquivo(String chaveArquivo) {
        this.chaveArquivo = chaveArquivo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }

    public Integer getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(Integer origemSistema) {
        this.origemSistema = origemSistema;
    }

    public String getInformacoesDesfazer() {
        return informacoesDesfazer;
    }

    public void setInformacoesDesfazer(String informacoesDesfazer) {
        this.informacoesDesfazer = informacoesDesfazer;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public JustificativaOperacaoDTO getTipoJustificativa() {
        return tipoJustificativa;
    }

    public void setTipoJustificativa(JustificativaOperacaoDTO tipoJustificativa) {
        this.tipoJustificativa = tipoJustificativa;
    }

    public UsuarioDTO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioDTO responsavel) {
        this.responsavel = responsavel;
    }

    public UsuarioDTO getResponsavelLiberacao() {
        return responsavelLiberacao;
    }

    public void setResponsavelLiberacao(UsuarioDTO responsavelLiberacao) {
        this.responsavelLiberacao = responsavelLiberacao;
    }

    public Long getDiasOperacao() {
        return diasOperacao;
    }

    public void setDiasOperacao(Long diasOperacao) {
        this.diasOperacao = diasOperacao;
    }

    public String getUrlArquivoAtestado() {
        return urlArquivoAtestado;
    }

    public void setUrlArquivoAtestado(String urlArquivoAtestado) {
        this.urlArquivoAtestado = urlArquivoAtestado;
    }

    public boolean isPermiteEstornar() {
        return permiteEstornar;
    }

    public void setPermiteEstornar(boolean permiteEstornar) {
        this.permiteEstornar = permiteEstornar;
    }

    public String getTipoOperacaoApresentar() {
        if (tipoOperacao == null) {
            tipoOperacao = "";
        }
        if (tipoOperacao.equals("RM")) {
            return "Rematrícula";
        }
        if (tipoOperacao.equals("TS")) {
            return "Transferência Saída";
        }
        if (tipoOperacao.equals("MA")) {
            return "Matrícula";
        }
        if (tipoOperacao.equals("AD")) {
            return "Alteração Duração";
        }
        if (tipoOperacao.equals("RE")) {
            return "Renovação";
        }
        if (tipoOperacao.equals("BA")) {
            return "Bônus- Acréscimo de dias ";
        }
        if (tipoOperacao.equals("BR")) {
            return "Bônus -Redução de dias ";
        }
        if (tipoOperacao.equals("CR")) {
            if (this.getDataOperacao() != null && this.getDataInicioEfetivacaoOperacao() != null
                    && this.getDataFimEfetivacaoOperacao() != null) {

                if (Calendario.maior(this.getDataOperacao(), this.getDataInicioEfetivacaoOperacao())
                        && Calendario.maior(this.getDataOperacao(), this.getDataFimEfetivacaoOperacao())) {
                    return "Férias (RETROATIVO)";
                }

            }
            return "Férias";
        }

        if (tipoOperacao.equals("TE")) {
            return "Transferência Entrada";
        }
        if (tipoOperacao.equals("CA")) {
            return "Cancelamento";
        }
        if (tipoOperacao.equals("AH")) {
            return "Alteração Horário";
        }
        if (tipoOperacao.equals("TR")) {
            return "Trancamento";
        }
        if (tipoOperacao.equals("TV")) {
            return "Trancamento Vencido";
        }
        if (tipoOperacao.equals("RT")) {
            return "Retorno Trancamento";
        }
        if (tipoOperacao.equals("IM")) {
            return "Incluir Modalidade";
        }
        if (tipoOperacao.equals("EM")) {
            return "Excluir Modalidade";
        }
        if (tipoOperacao.equals("AM")) {
            return "Alterar Modalidade";
        }
        if (tipoOperacao.equals("AC")) {
            return "Alteração Contrato";
        }
        if (tipoOperacao.equals("AT")) {
            if (this.getDataOperacao() != null && this.getDataInicioEfetivacaoOperacao() != null
                    && this.getDataFimEfetivacaoOperacao() != null) {

                if (Calendario.maior(this.getDataOperacao(), this.getDataInicioEfetivacaoOperacao())
                        && Calendario.maior(this.getDataOperacao(), this.getDataFimEfetivacaoOperacao())) {
                    return "Atestado (RETROATIVO)";
                }

            }
            return "Atestado";
        }

        if (tipoOperacao.equals("RA")) {
            return "Retorno - Atestado";
        }
        if (tipoOperacao.equals("LV"))
            return "Liberação de Vaga";

        if (tipoOperacao.equals("BC"))
            return "Afastamento Coletivo";

        if (tipoOperacao.equals("TD")) {
            return "Transferência dos Direitos de uso";
        }

        if (tipoOperacao.equals("RD")) {
            return "Retorno dos Direitos de uso";
        }


        return tipoOperacao;
    }
}
