package com.pacto.adm.core.swagger.respostas.contratooperacao;

import com.pacto.adm.core.dto.ClientesCanceladosDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo clientes com contratos cancelados com paginação")
public class ExemploRespostaListClientesCanceladosPaginacao extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ClientesCanceladosDTO> content;

    public List<ClientesCanceladosDTO> getContent() {
        return content;
    }

    public void setContent(List<ClientesCanceladosDTO> content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": [{"
            + "    \"codigoCliente\": 12345,"
            + "    \"matriculaCliente\": \"001234\","
            + "    \"nomeCliente\": \"<PERSON>\","
            + "    \"codigoContrato\": 98765,"
            + "    \"descricaoJustificativa\": \"Transferência para outro aluno\","
            + "    \"responsavelOperacao\": \"Maria Oliveira\","
            + "    \"dataOperacao\": \"2024-01-15T10:30:00.000Z\""
            + "  }, {"
            + "    \"codigoCliente\": 12346,"
            + "    \"matriculaCliente\": \"001235\","
            + "    \"nomeCliente\": \"Ana Costa Pereira\","
            + "    \"codigoContrato\": 98766,"
            + "    \"descricaoJustificativa\": \"Mudança de cidade\","
            + "    \"responsavelOperacao\": \"Carlos Santos\","
            + "    \"dataOperacao\": \"2024-01-20T14:45:00.000Z\""
            + "  }, {"
            + "    \"codigoCliente\": 12347,"
            + "    \"matriculaCliente\": \"001236\","
            + "    \"nomeCliente\": \"Pedro Almeida Silva\","
            + "    \"codigoContrato\": 98767,"
            + "    \"descricaoJustificativa\": \"Problemas de saúde\","
            + "    \"responsavelOperacao\": \"Fernanda Lima\","
            + "    \"dataOperacao\": \"2024-01-25T16:20:00.000Z\""
            + "  }],"
            + "  \"totalElements\": 3,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
