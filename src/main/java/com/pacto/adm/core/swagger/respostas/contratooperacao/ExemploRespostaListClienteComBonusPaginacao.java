package com.pacto.adm.core.swagger.respostas.contratooperacao;

import com.pacto.adm.core.dto.ClienteComBonusDTO;
import com.pacto.adm.core.dto.enveloperesposta.EnvelopeRespostaPaginacaoDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo clientes com bônus com paginação")
public class ExemploRespostaListClienteComBonusPaginacao extends EnvelopeRespostaPaginacaoDTO {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<ClienteComBonusDTO> content;

    public List<ClienteComBonusDTO> getContent() {
        return content;
    }

    public void setContent(List<ClienteComBonusDTO> content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": [{"
            + "    \"cliente\": {"
            + "      \"codigo\": 12345,"
            + "      \"situacao\": \"AT\","
            + "      \"codigoMatricula\": 1234,"
            + "      \"matricula\": \"001234\","
            + "      \"pessoa\": {"
            + "        \"codigo\": 67890,"
            + "        \"nome\": \"João Silva Santos\","
            + "        \"cpf\": \"123.456.789-00\""
            + "      },"
            + "      \"dataInicioContrato\": \"15-01-2024\","
            + "      \"dataTerminoContrato\": \"15-01-2025\","
            + "      \"telefones\": \"(11) 99999-9999\""
            + "    },"
            + "    \"contrato\": {"
            + "      \"codigo\": 98765,"
            + "      \"tipo\": \"MENSAL\","
            + "      \"vigenciaDe\": \"2024-01-15T00:00:00.000Z\","
            + "      \"vigenciaAte\": \"2025-01-15T00:00:00.000Z\","
            + "      \"situacao\": \"ATIVO\""
            + "    },"
            + "    \"contratoOperacao\": {"
            + "      \"codigo\": 11223,"
            + "      \"tipoOperacao\": \"BA\","
            + "      \"dataOperacao\": \"2024-01-20T14:30:00.000Z\","
            + "      \"valor\": 50.00,"
            + "      \"nrDiasOperacao\": 5,"
            + "      \"observacao\": \"Bônus de 5 dias concedido por indicação de novo cliente\""
            + "    }"
            + "  }, {"
            + "    \"cliente\": {"
            + "      \"codigo\": 12346,"
            + "      \"situacao\": \"AT\","
            + "      \"codigoMatricula\": 1235,"
            + "      \"matricula\": \"001235\","
            + "      \"pessoa\": {"
            + "        \"codigo\": 67891,"
            + "        \"nome\": \"Maria Oliveira Costa\","
            + "        \"cpf\": \"987.654.321-00\""
            + "      },"
            + "      \"dataInicioContrato\": \"20-02-2024\","
            + "      \"dataTerminoContrato\": \"20-02-2025\","
            + "      \"telefones\": \"(11) 88888-8888\""
            + "    },"
            + "    \"contrato\": {"
            + "      \"codigo\": 98766,"
            + "      \"tipo\": \"ANUAL\","
            + "      \"vigenciaDe\": \"2024-02-20T00:00:00.000Z\","
            + "      \"vigenciaAte\": \"2025-02-20T00:00:00.000Z\","
            + "      \"situacao\": \"ATIVO\""
            + "    },"
            + "    \"contratoOperacao\": {"
            + "      \"codigo\": 11224,"
            + "      \"tipoOperacao\": \"BC\","
            + "      \"dataOperacao\": \"2024-02-25T09:15:00.000Z\","
            + "      \"valor\": 100.00,"
            + "      \"nrDiasOperacao\": 10,"
            + "      \"observacao\": \"Bônus coletivo de 10 dias por campanha promocional\""
            + "    }"
            + "  }],"
            + "  \"totalElements\": 2,"
            + "  \"totalPages\": 1,"
            + "  \"first\": true,"
            + "  \"last\": true,"
            + "  \"size\": 10,"
            + "  \"number\": 0"
            + "}";
}
